import os

#
import httpx

#
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# https://github.com/alan-turing-institute/ReadabiliPy
import readabilipy.simple_json

# https://github.com/matthewwithanm/python-markdownify
import markdownify


HTML2MD_LOGO = """
██╗  ██╗████████╗███╗   ███╗██╗     ██████╗ ███╗   ███╗██████╗
██║  ██║╚══██╔══╝████╗ ████║██║     ╚════██╗████╗ ████║██╔══██╗
███████║   ██║   ██╔████╔██║██║      █████╔╝██╔████╔██║██║  ██║
██╔══██║   ██║   ██║╚██╔╝██║██║     ██╔═══╝ ██║╚██╔╝██║██║  ██║
██║  ██║   ██║   ██║ ╚═╝ ██║███████╗███████╗██║ ╚═╝ ██║██████╔╝
╚═╝  ╚═╝   ╚═╝   ╚═╝     ╚═╝╚══════╝╚══════╝╚═╝     ╚═╝╚═════╝
"""

console = Console()


def fetch_url(url: str) -> str:
    """
    Fetch content from a URL using httpx.

    Args:
        url (str): The URL to fetch
        console (Console): Rich console instance for output

    Returns:
        str: The response content as text
    """
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task(f"Fetching {url}...", total=None)

            with httpx.Client() as client:
                response = client.get(
                    url,
                    headers={"User-Agent": "Mozilla/5.0"},
                    timeout=30.0,
                    follow_redirects=True,
                )
                response.raise_for_status()
                progress.update(task, completed=True)

        console.print(f"✅ Successfully fetched content from {url}", style="green")

        page_raw = response.text
        content_type = response.headers.get("content-type", "")

        is_page_html = (
            "<html" in page_raw[:100] or "text/html" in content_type or not content_type
        )

        if is_page_html:
            return extract_content_from_html(page_raw)

        console.print(f"❌ No HTML for {url}", style="red")
        return ""
    except httpx.RequestError as e:
        console.print(f"❌ Network error while requesting {url}: {e}", style="red")
        return ""
    except httpx.HTTPStatusError as e:
        console.print(
            f"❌ HTTP error {e.response.status_code} while requesting {url}",
            style="red",
        )
        return ""


def extract_content_from_html(html: str) -> str:
    """Extract and convert HTML content to Markdown format.

    Args:
        html: Raw HTML content to process

    Returns:
        Simplified markdown version of the content
    """
    ret = readabilipy.simple_json.simple_json_from_html_string(
        html, use_readability=True
    )

    if not ret["content"]:
        console.print("❌ Page failed to be simplified from HTML", style="bold red")
        return ""

    content = markdownify.markdownify(
        ret["content"],
        heading_style=markdownify.ATX,
    )
    return content


def main():
    try:
        url = "https://zonetuto.fr/"
        console.print(f"\n🌐 Target URL: [cyan]{url}[/cyan]")

        name_without_suff = "Test"
        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_md_dir = os.path.join(script_dir, "output", name_without_suff)

        console.print(
            Panel.fit(
                f"[bold cyan]{HTML2MD_LOGO}[/bold cyan]\n"
                "[bold blue]📝 Conversion HTML vers Markdown[/bold blue]",
                border_style="blue",
            )
        )

        console.print(f"📁 [cyan]Répertoire de sortie:[/cyan] {local_md_dir}")
        os.makedirs(local_md_dir, exist_ok=True)

        md_content = fetch_url(url)
        if md_content:
            output_md_path = os.path.join(local_md_dir, f"html2md_{name_without_suff}.md")
            with open(output_md_path, "w", encoding="utf-8") as f:
                f.write(md_content)

            console.print(
                f"💾 [bold green]Résultat sauvegardé:[/bold green] [link]{output_md_path}[/link]"
            )
        else:
            console.print("❌ Failed to fetch content", style="bold red")
    except KeyboardInterrupt:
        return


if __name__ == "__main__":
    main()
